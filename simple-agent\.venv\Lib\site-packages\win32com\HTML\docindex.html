<HTML>
<HEAD>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html" charset="utf-8">
<META NAME="Generator" CONTENT="Microsoft Word 97">
<TITLE>win32com Documentation Index</TITLE>
<META NAME="Template" CONTENT="D:\Program Files\Microsoft Office\Office\html.dot">
</HEAD>
<BODY LINK="#0000ff" VLINK="#800080">

<H1><IMG SRC="image/pycom_blowing.gif" WIDTH=549 HEIGHT=99 ALT="Python and COM - Blowing the others away"></H1>
<H1>PythonCOM Documentation Index</H1>
<P>The following documentation is available</P>
<P><A HREF="QuickStartClientCom.html">A Quick Start to Client Side COM</A> (including makepy)</P>
<P><A HREF="QuickStartServerCom.html">A Quick Start to Server Side COM</A></P>
<P><A HREF="GeneratedSupport.html">Information on generated Python files (ie, what makepy generates)</A></P>
<P><A HREF="variant.html">An advanced VARIANT object which can give more control over parameter types</A></P>
<P><A HREF="COM_Records.html">COM Record Support</A></P>
<P><A HREF="package.html">A brief description of the win32com package structure</A></P>
<P><A HREF="PythonCOM.html">Python COM Implementation documentation</A></P>
<P><A HREF="misc.html">Misc stuff I don't know where to put anywhere else</A></P>
<H3>ActiveX Scripting</H3>
<P><A HREF="../../win32comext/axscript/demos/client/ie/demo.htm">ActiveX Scripting Demos</A></P></BODY>
</HTML>
