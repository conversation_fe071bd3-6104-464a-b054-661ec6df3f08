// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.type;

option go_package = "google.golang.org/genproto/googleapis/type/month;month";
option java_multiple_files = true;
option java_outer_classname = "MonthProto";
option java_package = "com.google.type";
option objc_class_prefix = "GTP";

// Represents a month in the Gregorian calendar.
enum Month {
  // The unspecified month.
  MONTH_UNSPECIFIED = 0;

  // The month of January.
  JANUARY = 1;

  // The month of February.
  FEBRUARY = 2;

  // The month of March.
  MARCH = 3;

  // The month of April.
  APRIL = 4;

  // The month of May.
  MAY = 5;

  // The month of June.
  JUNE = 6;

  // The month of July.
  JULY = 7;

  // The month of August.
  AUGUST = 8;

  // The month of September.
  SEPTEMBER = 9;

  // The month of October.
  OCTOBER = 10;

  // The month of November.
  NOVEMBER = 11;

  // The month of December.
  DECEMBER = 12;
}
