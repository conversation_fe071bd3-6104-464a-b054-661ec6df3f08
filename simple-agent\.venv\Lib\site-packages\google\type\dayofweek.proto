// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.type;

option go_package = "google.golang.org/genproto/googleapis/type/dayofweek;dayofweek";
option java_multiple_files = true;
option java_outer_classname = "DayOfWeekProto";
option java_package = "com.google.type";
option objc_class_prefix = "GTP";

// Represents a day of the week.
enum DayOfWeek {
  // The day of the week is unspecified.
  DAY_OF_WEEK_UNSPECIFIED = 0;

  // Monday
  MONDAY = 1;

  // Tuesday
  TUESDAY = 2;

  // Wednesday
  WEDNESDAY = 3;

  // Thursday
  THURSDAY = 4;

  // Friday
  FRIDAY = 5;

  // Saturday
  SATURDAY = 6;

  // Sunday
  SUNDAY = 7;
}
