from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from langchain_mcp_adapters.tools import load_mcp_tools
from langgraph.prebuilt import create_react_agent
from langchain_google_genai import ChatG<PERSON>gleGenerativeAI
from dotenv import load_dotenv
import asyncio
import os

load_dotenv()

model = ChatGoogleGenerativeAI(
    model="gemini-1.5-flash",
    temperature=0,
    google_api_key=os.getenv("GOOGLE_API_KEY")
)

server_params = StdioServerParameters(
    command="npx",
    env={
        "FIRECRAWL_API_KEY": os.getenv("FIRECRAWL_API_KEY"),
    },
    args=["firecrawl-mcp"]
)

async def main():
    async with stdio_client(server_params)as (read,write):
        async with ClientSession(read,write) as session : 
            await session.initialize()
            tools = await load_mcp_tools(session)
            agent = create_react_agent(model,tools)
            messages = [
                {
                    "role": "system",
                    "content": """You are a helpful assistant with access to powerful web scraping and research tools via Firecrawl.

IMPORTANT: You MUST use these tools when users ask for current information from the web. DO NOT say you cannot access the internet!

Available tools you MUST use:
- firecrawl_search: Search the web for any information
- firecrawl_scrape: Scrape content from specific URLs
- firecrawl_crawl: Crawl entire websites
- firecrawl_extract: Extract specific data from pages
- firecrawl_deep_research: Perform comprehensive research on topics

ALWAYS follow this process:
1. When asked about current information (products, rankings, documentation, etc.), FIRST use firecrawl_search
2. If given a specific URL, use firecrawl_scrape to get the content
3. If you need comprehensive research, use firecrawl_deep_research
4. NEVER respond with "I don't have access" - USE THE TOOLS!

Examples of what you CAN and SHOULD do:
- Search for "top headphones Amazon 2024" using firecrawl_search
- Scrape specific websites like documentation sites
- Research current trends and rankings
- Find latest product information

Remember: You have web access through these tools - USE THEM!"""
                }
            ]
            print("Available Tools -:", *[tool.name for tool in tools])
            print("-" * 60)
            
            while True:
                user_input = input("\n You:")
                if user_input == "quit" :
                    print("GoodBye")
                    break
                
                messages.append({"role":"user", "content":user_input[:175000]})
                try:
                    agent_response = await agent.ainvoke({"messages":messages})
                    
                    ai_message = agent_response["messages"][-1].content
                    print("\n Agent:", ai_message)
                except Exception as e:
                    print("Error:",e)
                        
                                      
if __name__ == "__main__":
    asyncio.run(main())